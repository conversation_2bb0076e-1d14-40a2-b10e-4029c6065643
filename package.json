{"name": "homesafe-frontend", "private": true, "version": "0.0.0", "type": "module", "scripts": {"dev": "vite", "build": "tsc -b && vite build", "build-only": "vite build", "lint": "eslint 'src/**/*.{js,jsx,ts,tsx}'", "lint:fix": "eslint 'src/**/*.{js,jsx,ts,tsx}' --fix", "format": "prettier --write 'src/**/*.{js,jsx,ts,tsx,css,json}'", "preview": "vite preview --host", "test": "vitest run", "test:watch": "vitest watch", "test:coverage": "vitest run --coverage"}, "dependencies": {"@react-google-maps/api": "^2.20.6", "axios": "^1.9.0", "date-fns": "^4.1.0", "leaflet": "^1.9.4", "react": "^19.0.0", "react-dom": "^19.0.0", "react-icons": "^5.5.0", "react-leaflet": "^5.0.0", "react-router-dom": "^7.2.0", "react-webcam": "^7.2.0", "styled-components": "^6.1.15"}, "devDependencies": {"@babel/preset-env": "^7.26.9", "@babel/preset-typescript": "^7.26.0", "@eslint/js": "^9.21.0", "@tailwindcss/postcss": "^4.0.12", "@testing-library/dom": "^10.4.0", "@testing-library/jest-dom": "^6.6.3", "@testing-library/react": "^16.2.0", "@types/leaflet": "^1.9.17", "@types/react": "^19.0.10", "@types/react-dom": "^19.0.4", "@vitejs/plugin-react": "^4.3.4", "autoprefixer": "^10.4.21", "eslint": "^9.21.0", "eslint-config-prettier": "^10.0.2", "eslint-plugin-prettier": "^5.2.3", "eslint-plugin-react": "^7.37.4", "eslint-plugin-react-hooks": "^5.2.0", "eslint-plugin-react-refresh": "^0.4.19", "globals": "^15.15.0", "identity-obj-proxy": "^3.0.0", "jsdom": "^26.0.0", "postcss": "^8.5.3", "prettier": "^3.5.3", "tailwindcss": "^4.0.13", "ts-node": "^10.9.2", "typescript": "~5.7.2", "typescript-eslint": "^8.24.1", "vite": "^6.2.1", "vitest": "^3.0.7"}}