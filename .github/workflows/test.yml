name: Test Frontend Code

on:
  pull_request:
    branches:
      - staging
      - main
  workflow_call:

jobs:
  test:
    runs-on: ubuntu-latest
    steps:
      - name: Checkout Code
        uses: actions/checkout@v4

      - name: Setup Node.js
        uses: actions/setup-node@v3
        with:
          node-version: '20.x'

      - name: Install Dependencies
        run: npm install

      - name: Run Tests
        run: npm run test