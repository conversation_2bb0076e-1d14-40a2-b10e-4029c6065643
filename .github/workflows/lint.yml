name: Lint Frontend Code

on:
  workflow_call:

jobs:
  lint:
    name: Lint Code
    runs-on: ubuntu-latest

    steps:
      - name: Checkout Code
        uses: actions/checkout@v4

      - name: Setup Node.js
        uses: actions/setup-node@v3
        with:
          node-version: '20.x'

      - name: Install Dependencies
        run: npm install

      - name: Run ESLint
        run: npm run lint